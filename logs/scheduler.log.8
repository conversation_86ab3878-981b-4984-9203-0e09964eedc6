2025-08-25 08:34:14,636 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for rubis
2025-08-25 08:34:14,640 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for rubis
2025-08-25 08:34:14,642 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-25 08:34:14,645 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for rubis
2025-08-25 08:34:14,646 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for rubis
2025-08-25 08:34:14,648 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-25 08:34:14,652 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for rubis
2025-08-25 08:34:14,656 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-25 08:34:14,657 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-25 08:34:14,659 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for rubis
2025-08-25 08:34:14,665 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-25 08:34:14,667 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for rubis
2025-08-25 08:34:14,673 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-25 08:34:14,677 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-25 08:34:14,681 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-25 08:34:14,687 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-25 08:34:14,690 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-25 08:34:14,694 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for rubis
2025-08-25 08:34:14,698 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-25 08:34:14,700 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-25 08:34:14,703 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for rubis
2025-08-25 08:34:14,706 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for rubis
2025-08-25 08:34:14,710 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for rubis
2025-08-25 08:34:14,714 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-25 08:34:14,717 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-25 08:34:14,718 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-25 08:34:14,722 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-25 08:34:14,724 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for rubis
2025-08-25 08:34:14,729 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-25 08:34:14,734 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for rubis
2025-08-25 08:34:14,738 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for rubis
2025-08-25 08:34:14,741 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for rubis
2025-08-25 08:34:14,749 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for rubis
2025-08-25 08:34:14,751 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for rubis
2025-08-25 08:34:14,756 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for rubis
2025-08-25 08:34:14,757 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-25 08:34:14,759 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-25 08:34:14,763 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for rubis
2025-08-25 08:35:14,997 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-25 08:35:15,007 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-25 08:35:15,013 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for rubis
2025-08-25 08:35:15,018 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-25 08:35:15,021 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-25 08:35:15,022 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-25 08:35:15,024 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-25 08:35:15,027 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-25 08:35:15,028 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-25 08:35:15,030 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-25 08:35:15,033 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for rubis
2025-08-25 08:35:15,037 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for rubis
2025-08-25 08:35:15,039 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for rubis
2025-08-25 08:35:15,041 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-25 08:35:15,044 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for rubis
2025-08-25 08:35:15,050 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for rubis
2025-08-25 08:35:15,055 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-25 08:35:15,057 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-25 08:35:15,063 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-25 08:35:15,066 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-25 08:35:15,068 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for rubis
2025-08-25 08:35:15,071 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for rubis
2025-08-25 08:35:15,074 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-25 08:35:15,076 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-25 08:35:15,081 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-25 08:35:15,083 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for rubis
2025-08-25 08:35:15,088 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for rubis
2025-08-25 08:35:15,092 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for rubis
2025-08-25 08:35:15,097 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for rubis
2025-08-25 08:35:15,099 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for rubis
2025-08-25 08:35:15,102 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for rubis
2025-08-25 08:35:15,105 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-25 08:35:15,107 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for rubis
2025-08-25 08:35:15,109 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-25 08:35:15,111 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for rubis
2025-08-25 08:35:15,112 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for rubis
2025-08-25 08:35:15,114 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for rubis
2025-08-25 08:35:15,119 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for rubis
2025-08-25 08:37:15,697 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-25 08:37:15,712 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-25 08:37:15,720 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-25 08:37:15,786 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-25 08:37:15,817 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-25 08:37:15,826 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-25 08:38:16,212 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-25 08:38:16,228 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-25 08:38:16,233 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-25 08:38:16,244 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-25 08:38:16,256 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-25 08:38:16,265 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-25 08:39:16,527 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-25 08:39:16,534 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-25 08:39:16,558 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-25 08:39:16,569 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-25 08:39:16,591 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-25 08:39:16,600 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-25 08:40:16,811 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-25 08:40:16,827 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-25 08:40:16,832 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-25 08:40:16,846 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-25 08:40:16,864 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-25 08:40:16,865 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-25 08:41:17,108 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-08-25 08:41:17,112 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-25 08:41:17,120 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-08-25 08:41:17,130 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-25 08:41:17,149 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-25 08:41:17,166 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-25 08:41:17,190 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-25 08:41:17,202 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-25 08:42:17,251 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-08-25 08:42:17,279 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-25 08:42:17,287 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-25 08:42:17,291 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-08-25 08:42:17,326 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-25 08:42:17,329 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-25 08:42:17,347 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-25 08:42:17,352 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-25 08:43:17,604 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-08-25 08:43:17,627 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-25 08:43:17,644 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-25 08:43:17,651 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-25 08:43:17,671 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-08-25 08:43:17,691 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-25 08:43:17,707 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-25 08:43:17,709 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-25 08:44:18,117 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-25 08:44:18,138 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-25 08:44:18,143 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-08-25 08:44:18,149 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-08-25 08:44:18,150 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-25 08:44:18,157 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-25 08:44:18,167 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-25 08:44:18,179 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-25 08:45:18,633 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-25 08:45:18,645 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-25 08:45:18,672 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-25 08:45:18,685 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-25 08:45:18,688 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-08-25 08:45:18,710 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-08-25 08:45:18,712 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-25 08:45:18,728 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-25 08:46:18,993 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for rubis
2025-08-25 08:46:19,001 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for rubis
2025-08-25 08:46:19,003 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for rubis
2025-08-25 08:46:19,018 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for rubis
2025-08-25 08:46:19,023 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for rubis
2025-08-25 08:46:19,029 ERROR scheduler Skipped queueing wiki.wiki.doctype.wiki_page.search.build_index_in_background because it was found in queue for rubis
2025-08-25 08:46:19,046 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for rubis
2025-08-25 08:46:19,049 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for rubis
2025-08-25 08:46:19,077 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for rubis
2025-08-25 08:46:19,084 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-08-25 08:46:19,089 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for rubis
2025-08-25 08:46:19,090 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for rubis
2025-08-25 08:46:19,095 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-08-25 08:46:19,097 ERROR scheduler Skipped queueing vfd_providers.utils.utils.posting_all_vfd_invoices because it was found in queue for rubis
2025-08-25 08:47:19,303 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for rubis
2025-08-25 08:47:19,315 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for rubis
2025-08-25 08:47:19,321 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for rubis
2025-08-25 08:47:19,326 ERROR scheduler Skipped queueing wiki.wiki.doctype.wiki_page.search.build_index_in_background because it was found in queue for rubis
2025-08-25 08:47:19,333 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for rubis
2025-08-25 08:47:19,342 ERROR scheduler Skipped queueing vfd_providers.utils.utils.posting_all_vfd_invoices because it was found in queue for rubis
2025-08-25 08:47:19,347 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for rubis
2025-08-25 08:47:19,361 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for rubis
2025-08-25 08:47:19,378 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for rubis
2025-08-25 08:47:19,388 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for rubis
2025-08-25 08:47:19,399 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for rubis
2025-08-25 08:47:19,400 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for rubis
2025-08-25 08:49:19,863 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-25 08:49:19,875 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-25 08:49:19,879 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-25 08:49:19,886 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-25 08:49:19,890 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-25 08:49:19,912 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-25 08:50:20,248 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-25 08:50:20,273 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-25 08:50:20,313 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-25 08:50:20,326 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-25 08:50:20,377 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-25 08:50:20,384 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-25 09:01:23,434 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for rubis
2025-08-25 09:01:23,439 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-25 09:01:23,442 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for rubis
2025-08-25 09:01:23,444 ERROR scheduler Skipped queueing wiki.wiki.doctype.wiki_page.search.build_index_in_background because it was found in queue for rubis
2025-08-25 09:01:23,446 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for rubis
2025-08-25 09:01:23,454 ERROR scheduler Skipped queueing vfd_providers.utils.utils.posting_all_vfd_invoices because it was found in queue for rubis
2025-08-25 09:01:23,455 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-25 09:01:23,458 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-25 09:01:23,465 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-25 09:01:23,467 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-25 09:01:23,468 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-25 09:01:23,470 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for rubis
2025-08-25 09:01:23,471 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for rubis
2025-08-25 09:01:23,472 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-25 09:01:23,474 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for rubis
2025-08-25 09:01:23,475 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for rubis
2025-08-25 09:01:23,481 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-25 09:01:23,484 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for rubis
2025-08-25 09:01:23,487 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-25 09:01:23,490 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-25 09:01:23,491 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for rubis
2025-08-25 09:01:23,492 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-25 09:01:23,496 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for rubis
2025-08-25 09:01:23,498 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-25 09:01:23,499 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-25 09:01:23,501 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for rubis
2025-08-25 09:01:23,502 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-25 09:01:23,509 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for rubis
2025-08-25 09:01:23,513 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for rubis
2025-08-25 09:01:23,514 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for rubis
2025-08-25 09:01:23,519 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-25 09:01:23,524 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-25 09:01:23,527 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-25 09:01:23,530 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for rubis
2025-08-25 09:01:23,538 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for rubis
2025-08-25 09:01:23,542 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-25 09:01:23,546 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-25 09:01:23,548 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for rubis
2025-08-25 09:02:24,022 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-25 09:02:24,025 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-25 09:02:24,030 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-25 09:02:24,042 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-25 09:02:24,045 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-25 09:02:24,051 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-25 09:02:24,056 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-25 09:02:24,057 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-25 09:02:24,074 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-25 09:02:24,077 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-25 09:02:24,108 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-25 09:02:24,110 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-25 09:02:24,124 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-25 09:03:24,149 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-25 09:03:24,153 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-25 09:03:24,157 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-25 09:03:24,166 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-25 09:03:24,174 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-25 09:03:24,189 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-25 09:03:24,191 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-25 09:03:24,202 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-25 09:03:24,214 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-25 09:03:24,215 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-25 09:03:24,225 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-25 09:03:24,254 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-25 09:03:24,257 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-25 09:05:24,951 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-25 09:05:24,969 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-25 09:05:24,984 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-25 09:05:24,999 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-25 09:05:25,005 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-25 09:05:25,016 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-25 09:06:25,203 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-25 09:06:25,216 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-25 09:06:25,241 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-25 09:06:25,253 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-25 09:06:25,302 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-25 09:06:25,306 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-25 10:01:41,589 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-25 10:01:41,595 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-25 10:01:41,599 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for rubis
2025-08-25 10:01:41,603 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-25 10:01:41,616 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for rubis
2025-08-25 10:01:41,621 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for rubis
2025-08-25 10:01:41,625 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-25 10:01:41,626 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for rubis
2025-08-25 10:01:41,628 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for rubis
2025-08-25 10:01:41,630 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-25 10:01:41,634 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-25 10:01:41,636 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-25 10:01:41,639 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-25 10:01:41,640 ERROR scheduler Skipped queueing wiki.wiki.doctype.wiki_page.search.build_index_in_background because it was found in queue for rubis
2025-08-25 10:01:41,650 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-25 10:01:41,653 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for rubis
2025-08-25 10:01:41,655 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-25 10:01:41,658 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-25 10:01:41,659 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for rubis
2025-08-25 10:01:41,662 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for rubis
2025-08-25 10:01:41,664 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-25 10:01:41,666 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for rubis
2025-08-25 10:01:41,669 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-25 10:01:41,671 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for rubis
2025-08-25 10:01:41,673 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-25 10:01:41,674 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for rubis
2025-08-25 10:01:41,678 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for rubis
2025-08-25 10:01:41,681 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for rubis
2025-08-25 10:01:41,682 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for rubis
2025-08-25 10:01:41,684 ERROR scheduler Skipped queueing vfd_providers.utils.utils.posting_all_vfd_invoices because it was found in queue for rubis
2025-08-25 10:01:41,685 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-25 10:01:41,686 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-25 10:01:41,687 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for rubis
2025-08-25 10:01:41,694 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for rubis
2025-08-25 10:01:41,698 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-25 10:01:41,700 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-08-25 10:01:41,709 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-08-25 10:01:41,711 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for rubis
2025-08-25 10:01:41,718 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for rubis
2025-08-25 10:01:41,720 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-25 10:01:41,723 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-25 10:02:41,929 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-25 10:02:41,931 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-25 10:02:41,941 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-25 10:02:41,943 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-25 10:02:41,951 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-25 10:02:41,953 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-25 10:02:41,964 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-25 10:02:41,979 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-25 10:02:42,000 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-25 10:02:42,005 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-25 10:02:42,017 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-25 10:02:42,029 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-25 10:02:42,036 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-25 10:03:42,083 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-25 10:03:42,105 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-25 10:03:42,116 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-25 10:03:42,118 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-25 10:03:42,119 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-25 10:03:42,126 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-25 10:03:42,130 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-25 10:03:42,150 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-25 10:03:42,163 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-25 10:03:42,170 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-25 10:03:42,186 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-25 10:03:42,200 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-25 10:03:42,212 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-25 10:04:42,388 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-25 10:04:42,392 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-25 10:04:42,410 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-25 10:04:42,413 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-25 10:04:42,425 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-25 10:04:42,442 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-25 10:04:42,443 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-25 10:04:42,457 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-25 10:04:42,463 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-25 10:04:42,474 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-25 10:04:42,480 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-25 10:04:42,487 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-25 10:04:42,496 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-25 10:05:42,678 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-25 10:05:42,687 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-25 10:05:42,689 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-25 10:05:42,694 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-25 10:05:42,696 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-25 10:05:42,701 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-25 10:05:42,711 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-25 10:05:42,717 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-25 10:05:42,730 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-25 10:05:42,742 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-25 10:05:42,749 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-25 10:05:42,752 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-25 10:05:42,754 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-25 10:05:42,761 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-25 10:05:42,763 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-25 10:05:42,783 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-25 10:05:42,794 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-25 10:05:42,801 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-25 10:05:42,807 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-25 11:01:59,393 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for rubis
2025-08-25 11:01:59,395 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-08-25 11:01:59,397 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-25 11:01:59,400 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for rubis
2025-08-25 11:01:59,402 ERROR scheduler Skipped queueing vfd_providers.utils.utils.posting_all_vfd_invoices because it was found in queue for rubis
2025-08-25 11:01:59,405 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-25 11:01:59,409 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-25 11:01:59,410 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for rubis
2025-08-25 11:01:59,414 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-08-25 11:01:59,420 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-25 11:01:59,423 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for rubis
2025-08-25 11:01:59,426 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for rubis
2025-08-25 11:01:59,429 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-25 11:01:59,432 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for rubis
2025-08-25 11:01:59,433 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for rubis
2025-08-25 11:01:59,435 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-25 11:01:59,436 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for rubis
2025-08-25 11:01:59,438 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-25 11:01:59,439 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for rubis
2025-08-25 11:01:59,442 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-25 11:01:59,446 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-25 11:01:59,447 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for rubis
2025-08-25 11:01:59,451 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for rubis
2025-08-25 11:01:59,455 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for rubis
2025-08-25 11:01:59,456 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-25 11:01:59,459 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-25 11:01:59,463 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-25 11:01:59,468 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for rubis
2025-08-25 11:01:59,470 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for rubis
2025-08-25 11:01:59,476 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-25 11:01:59,478 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for rubis
2025-08-25 11:01:59,485 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-25 11:01:59,490 ERROR scheduler Skipped queueing wiki.wiki.doctype.wiki_page.search.build_index_in_background because it was found in queue for rubis
2025-08-25 11:01:59,492 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-25 11:01:59,494 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-25 11:01:59,496 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-25 11:01:59,497 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for rubis
2025-08-25 11:01:59,499 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for rubis
2025-08-25 11:01:59,502 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-25 11:01:59,505 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-25 11:02:59,524 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-25 11:02:59,526 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-25 11:02:59,529 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-25 11:02:59,539 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-25 11:02:59,552 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-25 11:02:59,557 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-25 11:02:59,584 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-25 11:02:59,588 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-25 11:02:59,595 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-25 11:02:59,611 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-25 11:02:59,620 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-25 11:02:59,622 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-25 11:02:59,626 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-25 11:03:59,817 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-25 11:03:59,826 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-25 11:03:59,839 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-25 11:03:59,847 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-25 11:03:59,851 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-25 11:03:59,855 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-25 11:03:59,868 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-25 11:03:59,875 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-25 11:03:59,880 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-25 11:03:59,882 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-25 11:03:59,892 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-25 11:03:59,901 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-25 11:03:59,919 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-25 12:01:17,205 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for rubis
2025-08-25 12:01:17,209 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-25 12:01:17,211 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-25 12:01:17,214 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for rubis
2025-08-25 12:01:17,216 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-25 12:01:17,221 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-08-25 12:01:17,223 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for rubis
2025-08-25 12:01:17,228 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for rubis
2025-08-25 12:01:17,233 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for rubis
2025-08-25 12:01:17,236 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for rubis
2025-08-25 12:01:17,238 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for rubis
2025-08-25 12:01:17,242 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-25 12:01:17,244 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-25 12:01:17,245 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for rubis
2025-08-25 12:01:17,250 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-25 12:01:17,254 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-25 12:01:17,258 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for rubis
2025-08-25 12:01:17,263 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-25 12:01:17,266 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for rubis
2025-08-25 12:01:17,269 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-25 12:01:17,272 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-25 12:01:17,277 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-25 12:01:17,280 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-25 12:01:17,284 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-08-25 12:01:17,286 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for rubis
2025-08-25 12:01:17,288 ERROR scheduler Skipped queueing wiki.wiki.doctype.wiki_page.search.build_index_in_background because it was found in queue for rubis
2025-08-25 12:01:17,294 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-25 12:01:17,297 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for rubis
2025-08-25 12:01:17,301 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for rubis
2025-08-25 12:01:17,303 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for rubis
2025-08-25 12:01:17,312 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-25 12:01:17,316 ERROR scheduler Skipped queueing vfd_providers.utils.utils.posting_all_vfd_invoices because it was found in queue for rubis
2025-08-25 12:01:17,318 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for rubis
2025-08-25 12:01:17,321 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-25 12:01:17,326 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-25 12:01:17,329 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-25 12:01:17,336 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for rubis
2025-08-25 12:01:17,346 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for rubis
2025-08-25 12:01:17,348 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for rubis
2025-08-25 12:01:17,361 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-25 12:01:17,369 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-25 12:01:17,376 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for rubis
2025-08-25 12:02:17,558 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for rubis
2025-08-25 12:02:17,566 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for rubis
2025-08-25 12:02:17,568 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for rubis
2025-08-25 12:02:17,575 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-25 12:02:17,579 ERROR scheduler Skipped queueing wiki.wiki.doctype.wiki_page.search.build_index_in_background because it was found in queue for rubis
2025-08-25 12:02:17,582 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for rubis
2025-08-25 12:02:17,584 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-25 12:02:17,585 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-25 12:02:17,587 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for rubis
2025-08-25 12:02:17,588 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for rubis
2025-08-25 12:02:17,596 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for rubis
2025-08-25 12:02:17,597 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for rubis
2025-08-25 12:02:17,599 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for rubis
2025-08-25 12:02:17,601 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for rubis
2025-08-25 12:02:17,605 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-25 12:02:17,607 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for rubis
2025-08-25 12:02:17,609 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-25 12:02:17,613 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-08-25 12:02:17,615 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-25 12:02:17,619 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-25 12:02:17,620 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-25 12:02:17,622 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-25 12:02:17,625 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-25 12:02:17,627 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for rubis
2025-08-25 12:02:17,629 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-25 12:02:17,630 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-25 12:02:17,632 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-08-25 12:02:17,635 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-25 12:02:17,637 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for rubis
2025-08-25 12:02:17,639 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for rubis
2025-08-25 12:02:17,642 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for rubis
2025-08-25 12:02:17,648 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-25 12:02:17,653 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-25 12:02:17,658 ERROR scheduler Skipped queueing vfd_providers.utils.utils.posting_all_vfd_invoices because it was found in queue for rubis
2025-08-25 12:02:17,660 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-25 12:02:17,662 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-25 12:02:17,665 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-25 12:02:17,668 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for rubis
2025-08-25 12:02:17,669 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for rubis
2025-08-25 12:02:17,672 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for rubis
2025-08-25 12:02:17,676 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-25 12:02:17,677 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for rubis
2025-08-25 12:03:18,144 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-25 12:03:18,148 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-25 12:03:18,153 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-25 12:03:18,176 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-25 12:03:18,187 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-25 12:03:18,200 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-25 12:03:18,204 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-25 12:03:18,211 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-25 12:03:18,213 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-25 12:03:18,221 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-25 12:03:18,232 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-25 12:03:18,248 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-25 12:03:18,253 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-25 12:04:18,293 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-25 12:04:18,314 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-25 12:04:18,340 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-25 12:04:18,344 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-25 12:04:18,362 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-25 12:04:18,365 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-25 12:04:18,375 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-25 12:04:18,377 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-25 12:04:18,389 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-25 12:04:18,410 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-25 12:04:18,450 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-25 12:04:18,452 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-25 12:04:18,460 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-25 12:05:19,119 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-25 12:05:19,148 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-25 12:05:19,154 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-25 12:05:19,166 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-25 12:05:19,171 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-25 12:05:19,195 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-25 12:05:19,205 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-25 12:05:19,207 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-25 12:05:19,229 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-25 12:05:19,232 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-25 12:05:19,235 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-25 12:05:19,239 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-25 12:05:19,264 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-25 12:06:19,558 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-25 12:06:19,568 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-25 12:06:19,573 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-25 12:06:19,593 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-25 12:06:19,604 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-25 12:06:19,622 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-25 12:06:19,633 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-25 12:06:19,657 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-25 12:06:19,677 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-25 12:06:19,698 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-25 12:06:19,703 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-25 12:06:19,714 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-25 12:06:19,724 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-25 12:07:19,759 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-25 12:07:19,768 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-25 12:07:19,775 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-25 12:07:19,782 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-25 12:07:19,793 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-25 12:07:19,797 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-25 12:07:19,813 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-25 12:07:19,825 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-25 12:07:19,863 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-25 12:07:19,868 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-25 12:07:19,870 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-25 12:07:19,882 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-25 12:07:19,899 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-25 12:08:20,161 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-25 12:08:20,181 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-25 12:08:20,234 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-25 12:08:20,242 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-25 12:08:20,263 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-25 12:08:20,271 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-25 12:08:20,274 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-25 12:08:20,276 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-25 12:08:20,288 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-25 12:08:20,301 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-25 12:08:20,313 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-25 12:08:20,315 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-25 12:08:20,317 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-25 13:01:37,833 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-25 13:01:37,871 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-25 13:01:37,873 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-25 13:01:37,958 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-25 13:01:37,987 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-25 13:01:38,032 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-25 13:01:38,122 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-25 13:01:38,148 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-25 13:01:38,150 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-25 13:01:38,171 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-25 13:01:38,178 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-25 13:01:38,185 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-25 13:01:38,199 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-25 14:01:55,347 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for rubis
2025-08-25 14:01:55,351 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for rubis
2025-08-25 14:01:55,353 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for rubis
2025-08-25 14:01:55,354 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-25 14:01:55,359 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for rubis
2025-08-25 14:01:55,361 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-25 14:01:55,367 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-25 14:01:55,373 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-08-25 14:01:55,381 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for rubis
2025-08-25 14:01:55,384 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-25 14:01:55,386 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for rubis
2025-08-25 14:01:55,387 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-25 14:01:55,389 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for rubis
2025-08-25 14:01:55,392 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for rubis
2025-08-25 14:01:55,393 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for rubis
2025-08-25 14:01:55,397 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-25 14:01:55,399 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-25 14:01:55,404 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-25 14:01:55,407 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for rubis
2025-08-25 14:01:55,411 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for rubis
2025-08-25 14:01:55,413 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for rubis
2025-08-25 14:01:55,415 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-25 14:01:55,418 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for rubis
2025-08-25 14:01:55,420 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for rubis
2025-08-25 14:01:55,428 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-25 14:01:55,430 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-25 14:01:55,431 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for rubis
2025-08-25 14:01:55,433 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-25 14:01:55,435 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-25 14:01:55,438 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-25 14:01:55,442 ERROR scheduler Skipped queueing wiki.wiki.doctype.wiki_page.search.build_index_in_background because it was found in queue for rubis
2025-08-25 14:01:55,444 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
